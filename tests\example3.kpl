PROGRAM  EXAMPLE3;  (* TOWER OF HANOI *)
VAR  I:INTEGER;  
     N:INTEGER;  
     P:INTEGER;  
     Q:INTEGER;
     C:CHAR;

PROCEDURE  HANOI(N:INTEGER;  S:INTEGER;  Z:INTEGER);
BEGIN
  IF  N != 0  THEN
    BEGIN
      CALL  HANOI(N-1,S,6-S-Z);
      I:=I+1;  
      CALL  WRITELN;
      CALL  WRITEI(I);  
      CALL  WRITEI(N);
      CALL  WRITEI(S);  
      CALL  WRITEI(Z);
      CALL  HANOI(N-1,6-S-Z,Z)
    END
END;  (*END OF HANOI*)

BEGIN
  FOR  N := 1  TO  4  DO  
    BEGIN
      FOR  I:=1  TO  4  DO  
        CALL  WRITEC(' ');
      C :=  READC;  
      CALL  WRITEC(C)
    END;
  P:=1;  
  Q:=2;
  FOR  N:=2  TO  4  DO
    BEGIN  
      I:=0;  
      CALL  HANOI(N,P,Q);  
      CALL  WRITELN  
    END
END.  (* TOWER OF HANOI *)
