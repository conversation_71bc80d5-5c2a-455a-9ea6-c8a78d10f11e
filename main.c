/* 
 * @copyright (c) 2008, <PERSON><PERSON><PERSON>, Hanoi University of Technology
 * <AUTHOR>
 * @version 1.0
 */

#include <stdio.h>
#include <stdlib.h>
#include "reader.h"
#include "parser.h"

/******************************************************************/

int main(int argc, char *argv[]) {
  if (argc <= 1) {
    printf("parser: no input file.\n");
    return -1;
  }

  if (compile(argv[1]) == IO_ERROR) {
    printf("Can\'t read input file!\n");
    return -1;
  }

  printf("hehe\n");
    
  return 0;
}
