/* 
 * @copyright (c) 2008, <PERSON><PERSON><PERSON>, Hanoi University of Technology
 * <AUTHOR>
 * @version 1.0
 */
#include <stdio.h>

#include <stdlib.h>

#include "reader.h"

#include "scanner.h"

#include "parser.h"

#include "semantics.h"

#include "error.h"

#include "debug.h"

Token * currentToken;
Token * lookAhead;

extern Type * intType;
extern Type * charType;
extern SymTab * symtab;

void scan(void) {
    Token * tmp = currentToken;
    currentToken = lookAhead;
    lookAhead = getValidToken();
    free(tmp);
}

void eat(TokenType tokenType) {
    if (lookAhead -> tokenType == tokenType) {
        scan();
    } else missingToken(tokenType, lookAhead -> lineNo, lookAhead -> colNo);
}

void compileProgram(void) {
    assert("Compiling program . . .\n");
    Object * program;

    eat(KW_PROGRAM);
    eat(TK_IDENT);

    program = createProgramObject(currentToken -> string);
    enterBlock(program -> progAttrs -> scope);

    eat(SB_SEMICOLON);

    compileBlock();
    eat(SB_PERIOD);

    exitBlock();
    assert("Done program!\n");
}

void compileBlock(void) {
    assert("Compiling block...\n");
    Object * constObj;
    ConstantValue * constValue;
    if (lookAhead -> tokenType == KW_CONST) {
        eat(KW_CONST);
        do {
            eat(TK_IDENT);
            // Check if a constant identifier is fresh in the block
            checkFreshIdent(currentToken -> string);
            // Create a constant object
            constObj = createConstantObject(currentToken -> string);
            eat(SB_EQ);
            // Get the constant value
            constValue = compileConstant();
            constObj -> constAttrs -> value = constValue;
            // Declare the constant object 
            declareObject(constObj);

            eat(SB_SEMICOLON);
        } while (lookAhead -> tokenType == TK_IDENT);

        compileBlock2();
    } else compileBlock2();
    assert("Done block!\n");
}

void compileBlock2(void) {
    assert("Compiling type declarations...\n");
    Object * typeObj;
    Type * actualType;

    if (lookAhead -> tokenType == KW_TYPE) {
        eat(KW_TYPE);

        do {
            eat(TK_IDENT);
            // Check if a type identifier is fresh in the block
            checkFreshIdent(currentToken -> string);
            // create a type object
            typeObj = createTypeObject(currentToken -> string);

            eat(SB_EQ);
            // Get the actual type
            actualType = compileType();
            typeObj -> typeAttrs -> actualType = actualType;
            // Declare the type object
            declareObject(typeObj);

            eat(SB_SEMICOLON);
        } while (lookAhead -> tokenType == TK_IDENT);

        compileBlock3();
    } else compileBlock3();
    assert("Done type declarations!\n");
}

void compileBlock3(void) {
    assert("Compiling variable declarations...\n");
    Object * varObj;
    Type * varType;

    if (lookAhead -> tokenType == KW_VAR) {
        eat(KW_VAR);

        do {
            eat(TK_IDENT);
            // Check if a variable identifier is fresh in the block
            checkFreshIdent(currentToken -> string);
            // Create a variable object      
            varObj = createVariableObject(currentToken -> string);

            eat(SB_COLON);
            // Get the variable type
            varType = compileType();
            varObj -> varAttrs -> type = varType;
            // Declare the variable object
            declareObject(varObj);

            eat(SB_SEMICOLON);
        } while (lookAhead -> tokenType == TK_IDENT);

        compileBlock4();
    } else compileBlock4();
    assert("Done variable declarations!\n");
}

void compileBlock4(void) {
    assert("Compiling subroutines...\n");
    compileSubDecls();
    compileBlock5();
    assert("Done subroutines!\n");
}

void compileBlock5(void) {
    assert("Compiling statements...\n");
    eat(KW_BEGIN);
    compileStatements();
    eat(KW_END);
    assert("Done statements!\n");
}

void compileSubDecls(void) {
    assert("Compiling subroutine declarations...\n");
    while ((lookAhead -> tokenType == KW_FUNCTION) || (lookAhead -> tokenType == KW_PROCEDURE)) {
        if (lookAhead -> tokenType == KW_FUNCTION)
            compileFuncDecl();
        else compileProcDecl();
    }
    assert("Done subroutine declarations!\n");
}

void compileFuncDecl(void) {
    assert("Compiling function...\n");
    Object * funcObj;
    Type * returnType;

    eat(KW_FUNCTION);
    eat(TK_IDENT);
    // Check if a function identifier is fresh in the block
    checkFreshIdent(currentToken -> string);
    // create the function object
    funcObj = createFunctionObject(currentToken -> string);
    // declare the function object
    declareObject(funcObj);
    // enter the function's block
    enterBlock(funcObj -> funcAttrs -> scope);
    // parse the function's parameters
    compileParams();
    eat(SB_COLON);
    // get the funtion's return type
    returnType = compileBasicType();
    funcObj -> funcAttrs -> returnType = returnType;

    eat(SB_SEMICOLON);
    compileBlock();
    eat(SB_SEMICOLON);
    // exit the function block
    exitBlock();
    assert("Done function!\n");
}

void compileProcDecl(void) {
    assert("Compiling procedure...\n");
    // TODO: check if the procedure identifier is fresh in the block
    Object * procObj;

    eat(KW_PROCEDURE);
    eat(TK_IDENT);
    // Check if a procedure identifier is fresh in the block
    checkFreshIdent(currentToken -> string);
    // create a procedure object
    procObj = createProcedureObject(currentToken -> string);
    // declare the procedure object
    declareObject(procObj);
    // enter the procedure's block
    enterBlock(procObj -> procAttrs -> scope);
    // parse the procedure's parameters
    compileParams();

    eat(SB_SEMICOLON);
    compileBlock();
    eat(SB_SEMICOLON);
    // exit the block
    exitBlock();
    assert("Done procedure!\n");
}

void compileLValue(void)
{
  assert("Compiling lvalue...\n");
  Object *var;

  eat(TK_IDENT);
  // check if the identifier is a function identifier, or a variable identifier, or a parameter
  var = checkDeclaredLValueIdent(currentToken->string);
  switch (var->kind)
  {
  case OBJ_VARIABLE:
    compileIndexes();
  case OBJ_PARAMETER:
    break;
  case OBJ_FUNCTION:
    // TODO ERR_INVALID_RETURN
    if ((lookAhead->tokenType != SB_ASSIGN) || (var != symtab->currentScope->owner))
    {
      error(ERR_INVALID_RETURN, currentToken->lineNo, currentToken->colNo);
    }
    break;

  default:
    error(ERR_INVALID_LVALUE, currentToken->lineNo, currentToken->colNo);
  }
  assert("Done lvalue!\n");
}

ConstantValue* compileUnsignedConstant(void) {
    assert("Compiling unsigned constant...\n");
    ConstantValue* constValue;
    Object* obj;

    switch (lookAhead -> tokenType) {
    case TK_NUMBER:
        eat(TK_NUMBER);
        constValue = makeIntConstant(currentToken -> value);
        break;
    case TK_IDENT:
        eat(TK_IDENT);
        // check if the constant identifier is declared
        obj = checkDeclaredConstant(currentToken -> string);
        constValue = duplicateConstantValue(obj -> constAttrs -> value);

        break;
    case TK_CHAR:
        eat(TK_CHAR);
        constValue = makeCharConstant(currentToken -> string[0]);
        break;
    default:
        error(ERR_INVALID_CONSTANT, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }
    assert("Done unsigned constant!\n");
    return constValue;
}

ConstantValue* compileConstant(void) {
    assert("Compiling constant...\n");
    ConstantValue* constValue;

    switch (lookAhead -> tokenType) {
    case SB_PLUS:
        eat(SB_PLUS);
        constValue = compileConstant2();
        break;
    case SB_MINUS:
        eat(SB_MINUS);
        constValue = compileConstant2();
        constValue -> intValue = -constValue -> intValue;
        break;
    case TK_CHAR:
        eat(TK_CHAR);
        constValue = makeCharConstant(currentToken -> string[0]);
        break;
    default:
        constValue = compileConstant2();
        break;
    }
    assert("Done constant!\n");
    return constValue;
}

ConstantValue* compileConstant2(void) {
    assert("Compiling constant2...\n");
    ConstantValue* constValue;
    Object* obj;

    switch (lookAhead -> tokenType) {
    case TK_NUMBER:
        eat(TK_NUMBER);
        constValue = makeIntConstant(currentToken -> value);
        break;
    case TK_IDENT:
        eat(TK_IDENT);
        // check if the integer constant identifier is declared
        obj = checkDeclaredConstant(currentToken -> string);
        if (obj -> constAttrs -> value -> type == TP_INT)
            constValue = duplicateConstantValue(obj -> constAttrs -> value);
        else
            error(ERR_UNDECLARED_INT_CONSTANT, currentToken -> lineNo, currentToken -> colNo);
        break;
    default:
        error(ERR_INVALID_CONSTANT, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }
    assert("Done constant2!\n");
    return constValue;
}

Type* compileType(void) {
    assert("Compiling type...\n");
    Type* type;
    Type* elementType;
    int arraySize;
    Object* obj;

    switch (lookAhead -> tokenType) {
    case KW_INTEGER:
        eat(KW_INTEGER);
        type = makeIntType();
        break;
    case KW_CHAR:
        eat(KW_CHAR);
        type = makeCharType();
        break;
    case KW_ARRAY:
        eat(KW_ARRAY);
        eat(SB_LSEL);
        eat(TK_NUMBER);

        arraySize = currentToken -> value;

        eat(SB_RSEL);
        eat(KW_OF);
        elementType = compileType();
        type = makeArrayType(arraySize, elementType);
        break;
    case TK_IDENT:
        eat(TK_IDENT);
        // check if the type idntifier is declared
        obj = checkDeclaredType(currentToken -> string);
        type = duplicateType(obj -> typeAttrs -> actualType);
        break;
    default:
        error(ERR_INVALID_TYPE, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }
    assert("Done type!\n");
    return type;
}

Type* compileBasicType(void) {
    assert("Compiling basic type...\n");
    Type* type;

    switch (lookAhead -> tokenType) {
    case KW_INTEGER:
        eat(KW_INTEGER);
        type = makeIntType();
        break;
    case KW_CHAR:
        eat(KW_CHAR);
        type = makeCharType();
        break;
    default:
        error(ERR_INVALID_BASICTYPE, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }
    assert("Done basic type!\n");
    return type;
}

void compileParams(void) {
    assert("Compiling parameters...\n");
    if (lookAhead -> tokenType == SB_LPAR) {
        eat(SB_LPAR);
        compileParam();
        while (lookAhead -> tokenType == SB_SEMICOLON) {
            eat(SB_SEMICOLON);
            compileParam();
        }
        eat(SB_RPAR);
    }
    assert("Done parameters!\n");
}

void compileParam(void) {
    assert("Compiling parameter...\n");
    Object * param;
    Type * type;
    enum ParamKind paramKind;

    switch (lookAhead -> tokenType) {
    case TK_IDENT:
        paramKind = PARAM_VALUE;
        break;
    case KW_VAR:
        eat(KW_VAR);
        paramKind = PARAM_REFERENCE;
        break;
    default:
        error(ERR_INVALID_PARAMETER, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }

    eat(TK_IDENT);
    // check if the parameter identifier is fresh in the block
    printf("%s\n", currentToken->string);
    checkFreshIdent(currentToken -> string);
    printf("fresh checked\n");
    param = createParameterObject(currentToken -> string, paramKind, symtab -> currentScope -> owner);
    printf("Created\n");
    eat(SB_COLON);
    type = compileBasicType();
    param -> paramAttrs -> type = type;
    declareObject(param);
    assert("Done parameter!\n");
}

void compileStatements(void) {
    assert("Compiling statements...\n");
    compileStatement();
    while (lookAhead -> tokenType == SB_SEMICOLON) {
        eat(SB_SEMICOLON);
        compileStatement();
    }
    assert("Done statements!\n");
}

void compileStatement(void) {
    assert("Compiling statement...\n");
    switch (lookAhead -> tokenType) {
    case TK_IDENT:
        compileAssignSt();
        break;
    case KW_CALL:
        compileCallSt();
        break;
    case KW_BEGIN:
        compileGroupSt();
        break;
    case KW_IF:
        compileIfSt();
        break;
    case KW_WHILE:
        compileWhileSt();
        break;
    case KW_FOR:
        compileForSt();
        break;
        // EmptySt needs to check FOLLOW tokens
    case SB_SEMICOLON:
    case KW_END:
    case KW_ELSE:
        break;
        // Error occurs
    default:
        error(ERR_INVALID_STATEMENT, lookAhead -> lineNo, lookAhead -> colNo);
        break;
    }
    assert("Done statement!\n");
}

void compileAssignSt(void) {
    assert("Compiling assignment statement...\n");
    compileLValue();
    eat(SB_ASSIGN);
    compileExpression();
    assert("Done assignment statement!\n");
}

void compileCallSt(void) {
    assert("Compiling call statement...\n");
    eat(KW_CALL);
    eat(TK_IDENT);
    // check if the identifier is a declared procedure
    checkDeclaredProcedure(currentToken -> string);
    compileArguments();
    assert("Done call statement!\n");
}

void compileGroupSt(void) {
    assert("Compiling group statement...\n");
    eat(KW_BEGIN);
    compileStatements();
    eat(KW_END);
    assert("Done group statement!\n");
}

void compileIfSt(void) {
    assert("Compiling if statement...\n");
    eat(KW_IF);
    compileCondition();
    eat(KW_THEN);
    compileStatement();
    if (lookAhead -> tokenType == KW_ELSE)
        compileElseSt();
    assert("Done if statement!\n");
}

void compileElseSt(void) {
    assert("Compiling else statement...\n");
    eat(KW_ELSE);
    compileStatement();
    assert("Done else statement!\n");
}

void compileWhileSt(void) {
    assert("Compiling while statement...\n");
    eat(KW_WHILE);
    compileCondition();
    eat(KW_DO);
    compileStatement();
    assert("Done while statement!\n");
}

void compileForSt(void) {
    assert("Compiling for statement...\n");
    eat(KW_FOR);
    eat(TK_IDENT);

    // check if the identifier is a variable
    checkDeclaredVariable(currentToken -> string);

    eat(SB_ASSIGN);
    compileExpression();

    eat(KW_TO);
    compileExpression();

    eat(KW_DO);
    compileStatement();
    assert("Done for statement!\n");
}

void compileArgument(void) {
    assert("Compiling argument...\n");
    compileExpression();
    assert("Done argument!\n");
}

void compileArguments(void) {
    assert("Compiling arguments...\n");
    switch (lookAhead -> tokenType) {
    case SB_LPAR:
        eat(SB_LPAR);
        compileArgument();

        while (lookAhead -> tokenType == SB_COMMA) {
            eat(SB_COMMA);
            compileArgument();
        }

        eat(SB_RPAR);
        break;
        // Check FOLLOW set 
    case SB_TIMES:
    case SB_SLASH:
    case SB_PLUS:
    case SB_MINUS:
    case KW_TO:
    case KW_DO:
    case SB_RPAR:
    case SB_COMMA:
    case SB_EQ:
    case SB_NEQ:
    case SB_LE:
    case SB_LT:
    case SB_GE:
    case SB_GT:
    case SB_RSEL:
    case SB_SEMICOLON:
    case KW_END:
    case KW_ELSE:
    case KW_THEN:
        break;
    default:
        error(ERR_INVALID_ARGUMENTS, lookAhead -> lineNo, lookAhead -> colNo);
    }
    assert("Done arguments!\n");
}

void compileCondition(void) {
    assert("Compiling condition...\n");
    compileExpression();

    switch (lookAhead -> tokenType) {
    case SB_EQ:
        eat(SB_EQ);
        break;
    case SB_NEQ:
        eat(SB_NEQ);
        break;
    case SB_LE:
        eat(SB_LE);
        break;
    case SB_LT:
        eat(SB_LT);
        break;
    case SB_GE:
        eat(SB_GE);
        break;
    case SB_GT:
        eat(SB_GT);
        break;
    default:
        error(ERR_INVALID_COMPARATOR, lookAhead -> lineNo, lookAhead -> colNo);
    }

    compileExpression();
    assert("Done condition!\n");
}

void compileExpression(void) {
    assert("Compiling expression...\n");
    switch (lookAhead -> tokenType) {
    case SB_PLUS:
        eat(SB_PLUS);
        compileExpression2();
        break;
    case SB_MINUS:
        eat(SB_MINUS);
        compileExpression2();
        break;
    default:
        compileExpression2();
    }
    assert("Done expression!\n");
}

void compileExpression2(void) {
    assert("Compiling expression2...\n");
    compileTerm();
    compileExpression3();
    assert("Done expression2!\n");
}

void compileExpression3(void) {
    assert("Compiling expression3...\n");
    switch (lookAhead -> tokenType) {
    case SB_PLUS:
        eat(SB_PLUS);
        compileTerm();
        compileExpression3();
        break;
    case SB_MINUS:
        eat(SB_MINUS);
        compileTerm();
        compileExpression3();
        break;
        // check the FOLLOW set
    case KW_TO:
    case KW_DO:
    case SB_RPAR:
    case SB_COMMA:
    case SB_EQ:
    case SB_NEQ:
    case SB_LE:
    case SB_LT:
    case SB_GE:
    case SB_GT:
    case SB_RSEL:
    case SB_SEMICOLON:
    case KW_END:
    case KW_ELSE:
    case KW_THEN:
        break;
    default:
        error(ERR_INVALID_EXPRESSION, lookAhead -> lineNo, lookAhead -> colNo);
    }
    assert("Done expression3!\n");
}

void compileTerm(void) {
    assert("Compiling term...\n");
    compileFactor();
    compileTerm2();
    assert("Done term!\n");
}

void compileTerm2(void) {
    assert("Compiling term2...\n");
    switch (lookAhead -> tokenType) {
    case SB_TIMES:
        eat(SB_TIMES);
        compileFactor();
        compileTerm2();
        break;
    case SB_SLASH:
        eat(SB_SLASH);
        compileFactor();
        compileTerm2();
        break;
        // check the FOLLOW set
    case SB_PLUS:
    case SB_MINUS:
    case KW_TO:
    case KW_DO:
    case SB_RPAR:
    case SB_COMMA:
    case SB_EQ:
    case SB_NEQ:
    case SB_LE:
    case SB_LT:
    case SB_GE:
    case SB_GT:
    case SB_RSEL:
    case SB_SEMICOLON:
    case KW_END:
    case KW_ELSE:
    case KW_THEN:
        break;
    default:
        error(ERR_INVALID_TERM, lookAhead -> lineNo, lookAhead -> colNo);
    }
    assert("Done term2!\n");
}

void compileFactor(void) {
    assert("Compiling factor...\n");
    Object * obj;

    switch (lookAhead -> tokenType) {
    case TK_NUMBER:
        eat(TK_NUMBER);
        break;
    case TK_CHAR:
        eat(TK_CHAR);
        break;
    case TK_IDENT:
        eat(TK_IDENT);
        // check if the identifier is declared
        obj = checkDeclaredIdent(currentToken -> string);

        switch (obj -> kind) {
        case OBJ_CONSTANT:
            break;
        case OBJ_VARIABLE:
            compileIndexes();
            break;
        case OBJ_PARAMETER:
            break;
        case OBJ_FUNCTION:
            compileArguments();
            break;
        default:
            error(ERR_INVALID_FACTOR, currentToken -> lineNo, currentToken -> colNo);
            break;
        }
        break;
    default:
        error(ERR_INVALID_FACTOR, lookAhead -> lineNo, lookAhead -> colNo);
    }
    assert("Done factor!\n");
}

void compileIndexes(void) {
    while (lookAhead -> tokenType == SB_LSEL) {
        eat(SB_LSEL);
        compileExpression();
        eat(SB_RSEL);
    }
}

int compile(char * fileName) {
    if (openInputStream(fileName) == IO_ERROR)
        return IO_ERROR;

    currentToken = NULL;
    lookAhead = getValidToken();
    initSymTab();
    compileProgram();
    printObject(symtab -> program, 0);

    cleanSymTab();
    free(currentToken);
    free(lookAhead);
    closeInputStream();
    return IO_SUCCESS;

}